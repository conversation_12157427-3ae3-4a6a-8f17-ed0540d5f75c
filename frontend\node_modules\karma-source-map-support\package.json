{"name": "karma-source-map-support", "version": "1.4.0", "description": "Karma plugin for inline sourcemap support", "main": "lib/index.js", "dependencies": {"source-map-support": "^0.5.5"}, "homepage": "https://github.com/tschaub/karma-source-map-support", "author": {"name": "<PERSON>", "url": "http://tschaub.net/"}, "keywords": ["karma-plugin", "karma-framework", "browserify", "inline", "sourcemap"], "repository": {"type": "git", "url": "git://github.com/tschaub/karma-source-map-support.git"}, "bugs": {"url": "https://github.com/tschaub/karma-source-map-support/issues"}, "license": "MIT", "scripts": {"test": "eslint lib"}, "eslintConfig": {"extends": "<PERSON><PERSON><PERSON><PERSON>", "globals": {"sourceMapSupport": false}}, "devDependencies": {"eslint": "^5.13.0", "eslint-config-tschaub": "^12.0.1"}}