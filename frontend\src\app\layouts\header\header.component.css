.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  padding: 1rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: white !important;
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: rgba(255, 255, 255, 0.8) !important;
  transform: scale(1.05);
}

.navbar-nav .nav-link {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.navbar-toggler {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Boutons d'authentification */
.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
  color: white;
  transform: translateY(-2px);
}

.btn-light {
  background-color: white;
  color: #667eea;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
}

.btn-light:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Dropdown utilisateur */
.dropdown-toggle {
  border-radius: 8px;
  font-weight: 500;
}

.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
}

.dropdown-item {
  padding: 0.75rem 1.25rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0.25rem;
}

.dropdown-item:hover {
  background-color: #667eea;
  color: white;
  transform: translateX(5px);
}

.dropdown-item-text {
  padding: 0.5rem 1.25rem;
}

/* Responsive design */
@media (max-width: 991.98px) {
  .navbar {
    padding: 1rem;
  }

  .navbar-collapse {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-top: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .navbar-nav {
    margin-bottom: 1rem;
  }

  .nav-item {
    margin: 0.25rem 0;
    width: 100%;
  }

  .nav-link {
    padding: 0.75rem 1rem;
    width: 100%;
    text-align: left;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
    width: 100%;
  }

  .btn-outline-light,
  .btn-light {
    width: 100%;
    justify-content: center;
  }

  .dropdown {
    width: 100%;
  }

  .dropdown-toggle {
    width: 100%;
    justify-content: center;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.navbar {
  animation: fadeIn 0.5s ease-out;
}
