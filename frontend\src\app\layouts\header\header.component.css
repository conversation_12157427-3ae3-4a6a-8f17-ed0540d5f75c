.navbar {
  background-color: white;
  padding: 10px 50px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-size: 24px;
  font-weight: bold;
  color: #4786eb;
}

.navbar-nav .nav-link {
  font-size: 16px;
  color: #333;
  padding: 10px 20px;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: #4786eb;
}

.nav-item {
  margin-right: 15px;
}

.navbar-toggler {
  border: none;
  background: transparent;
}

.navbar-toggler-icon {
  color: #333;
}

.navbar-icons {
  display: flex;
  align-items: center;
}

.navbar-icons a {
  text-decoration: none;
  font-size: 20px;
  margin: 0 10px;
  position: relative;
  color: #333;
  transition: color 0.3s ease;
}

.navbar-icons a:hover {
  color: #4786eb;
}

.navbar-icons .notification-badge {
  color: white;
  background-color: #4786eb;
  border-radius: 50%;
  font-size: 10px;
  padding: 2px 5px;
  position: absolute;
  top: -8px;
  right: -10px;
}

.phone-number {
  font-size: 16px;
  margin-left: 10px;
  color: #333;
}

.btn-outline-success {
  border-color: #4786eb;
  color: #4786eb;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.btn-outline-success:hover {
  background-color: #4786eb;
  color: white;
}

.btn-primary {
  background-color: white;
  color: #4786eb;
  font-weight: bold;
  border: 0.5px solid #4786eb;
  transition: background-color 0.3s ease, color 0.3s ease;
  margin-top: 5px;
  margin-left: 20px;
}

.btn-primary:hover {
  background-color: #4786eb;
  color: white;
}

.navbar-dark .nav-link {
  color: white;
}

.navbar-dark .nav-link:hover {
  color: #4786eb;
}

.navbar .form-control {
  margin-left: 20px;
}

@media (max-width: 992px) {
  .navbar-collapse {
    position: fixed;
    top: 70px;
    right: 0;
    height: calc(100% - 70px);
    width: 250px;
    background-color: white;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 1050;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .navbar-collapse.show {
    transform: translateX(0);
  }

  .navbar-nav {
    flex-direction: column;
    align-items: flex-start;
  }

  .nav-item {
    width: 100%;
    margin-left: 2px;
  }

  .nav-link {
    padding: 15px;
    text-align: left;
    width: 100%;
  }

  .navbar-toggler {
    position: fixed;
    right: 10px;
    top: 10px;
    z-index: 1100;
  }

  .navbar-collapse form,
  .navbar-collapse .btn-primary {
    order: -1;
    margin-bottom: 15px;
    width: 100%;
  }

  .form-control {
    width: calc(100% - 40px);
    margin-left: 10px;
  }
}

.nav-link {
  cursor: pointer;
  color: #0d6efd;
  text-decoration: none;
}

.nav-link:hover {
  text-decoration: underline;
}
