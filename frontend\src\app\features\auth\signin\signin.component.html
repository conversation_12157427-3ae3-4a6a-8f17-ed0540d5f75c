<div class="auth-container">
  <div class="container-fluid vh-100">
    <div class="row h-100">
      <!-- Section gauche avec image/design -->
      <div class="col-lg-6 d-none d-lg-flex auth-left-section">
        <div class="d-flex flex-column justify-content-center align-items-center text-white p-5">
          <div class="text-center">
            <i class="fas fa-fish fa-5x mb-4 text-primary"></i>
            <h2 class="mb-3">Bienvenue sur miniProjetPoisson</h2>
            <p class="lead">Gérez votre catalogue de poissons en toute simplicité</p>
          </div>
        </div>
      </div>

      <!-- Section droite avec formulaire -->
      <div class="col-lg-6 d-flex align-items-center justify-content-center">
        <div class="auth-form-container">
          <div class="text-center mb-4">
            <h3 class="fw-bold text-primary">Connexion</h3>
            <p class="text-muted">Connectez-vous à votre compte</p>
          </div>

          <!-- Message d'erreur -->
          <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>

          <!-- Formulaire de connexion -->
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <!-- Email -->
            <div class="mb-3">
              <label for="email" class="form-label">
                <i class="fas fa-envelope me-2"></i>Email
              </label>
              <input
                type="email"
                class="form-control"
                id="email"
                formControlName="email"
                placeholder="<EMAIL>"
                [class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('email') }}
              </div>
            </div>

            <!-- Mot de passe -->
            <div class="mb-4">
              <label for="password" class="form-label">
                <i class="fas fa-lock me-2"></i>Mot de passe
              </label>
              <input
                type="password"
                class="form-control"
                id="password"
                formControlName="password"
                placeholder="Votre mot de passe"
                [class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('password') }}
              </div>
            </div>

            <!-- Bouton de connexion -->
            <button
              type="submit"
              class="btn btn-primary w-100 py-2 mb-3"
              [disabled]="isLoading"
            >
              <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i *ngIf="!isLoading" class="fas fa-sign-in-alt me-2"></i>
              {{ isLoading ? 'Connexion...' : 'Se connecter' }}
            </button>
          </form>

          <!-- Lien vers inscription -->
          <div class="text-center">
            <p class="mb-0">
              Pas encore de compte ?
              <a routerLink="/auth/register" class="text-primary text-decoration-none fw-bold">
                Créer un compte
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
