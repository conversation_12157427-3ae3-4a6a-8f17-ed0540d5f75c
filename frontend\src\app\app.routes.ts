import { Routes } from '@angular/router';
import { HomeComponent } from './layouts/home/<USER>';
import { authGuard, guestGuard } from './guards/auth.guard';

export const routes: Routes = [
  { path: 'home', component: HomeComponent },
  {
    path: 'poissons',
    loadComponent: () =>
      import('./features/list-poissons/list-poissons.component').then(
        (m) => m.ListPoissonsComponent
      ),
    canActivate: [authGuard]
  },
  {
    path: 'auth/login',
    loadComponent: () =>
      import('./features/auth/signin/signin.component').then(
        (m) => m.SigninComponent
      ),
    canActivate: [guestGuard]
  },
  {
    path: 'auth/register',
    loadComponent: () =>
      import('./features/auth/signup/signup.component').then(
        (m) => m.SignupComponent
      ),
    canActivate: [guestGuard]
  },
  { path: '', redirectTo: 'home', pathMatch: 'full' },
  { path: '**', redirectTo: 'home' }
];
