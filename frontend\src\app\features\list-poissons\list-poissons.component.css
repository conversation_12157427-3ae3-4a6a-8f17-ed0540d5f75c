.card {
  transition: transform 0.3s ease;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  background-color: white;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.2);
}

.card-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.card-text {
  font-size: 0.9rem;
  color: #555;
  margin: 0;
}

.card-img {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;
  height: 200px;
  object-fit: cover;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-form {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  width: 400px;
  max-width: 90%;
}


/* Overlay flouté semi-transparent, identique pour formulaire et détail */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
}

/* Conteneur blanc centré */
.modal-form {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  width: 400px;
  max-width: 90%;
  position: relative;
  cursor: auto;
  text-align: center;
}

/* Image avec arrondis en haut */
.card-img {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;
  height: 200px;
  object-fit: cover;
  margin-bottom: 15px;
}

/* Titres et texte */
.card-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.card-text {
  font-size: 0.9rem;
  color: #555;
  margin: 6px 0;
}

/* Bouton fermer (croix) */
.btn-close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 1.8rem;
  font-weight: bold;
  cursor: pointer;
  color: #333;
  line-height: 1;
}

.btn-close:hover {
  color: red;
}
/* Overlay flouté semi-transparent, identique pour formulaire et détail */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
}

/* Conteneur blanc centré */
.modal-form {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  width: 400px;
  max-width: 90%;
  position: relative;
  cursor: auto;
  text-align: center;
}

/* Image avec arrondis en haut */
.card-img {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;
  height: 200px;
  object-fit: cover;
  margin-bottom: 15px;
}

/* Titres et texte */
.card-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.card-text {
  font-size: 0.9rem;
  color: #555;
  margin: 6px 0;
}

/* Bouton fermer (croix) */
.btn-close {
  font-size: 1.5rem;
  font-weight: bold;
  color: gray;        /* couleur grise */
  background: transparent;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 15px;
  opacity: 1;         /* toujours visible */
  transition: none;   /* pas d'animation hover */
}

.btn-close:hover,
.btn-close:focus {
  color: gray;        /* même couleur au hover/focus */
  outline: none;
  box-shadow: none;
}

