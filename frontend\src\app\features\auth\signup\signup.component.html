<div class="auth-container">
  <div class="container-fluid vh-100">
    <div class="row h-100">
      <!-- Section gauche avec image/design -->
      <div class="col-lg-6 d-none d-lg-flex auth-left-section">
        <div class="d-flex flex-column justify-content-center align-items-center text-white p-5">
          <div class="text-center">
            <i class="fas fa-fish fa-5x mb-4 text-primary"></i>
            <h2 class="mb-3">Rejoignez-nous !</h2>
            <p class="lead">Créez votre compte et commencez à gérer vos poissons</p>
          </div>
        </div>
      </div>

      <!-- Section droite avec formulaire -->
      <div class="col-lg-6 d-flex align-items-center justify-content-center">
        <div class="auth-form-container">
          <div class="text-center mb-4">
            <h3 class="fw-bold text-primary">Inscription</h3>
            <p class="text-muted">C<PERSON>ez votre compte gratuitement</p>
          </div>

          <!-- Message d'erreur -->
          <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>

          <!-- Formulaire d'inscription -->
          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
            <!-- Nom -->
            <div class="mb-3">
              <label for="nom" class="form-label">
                <i class="fas fa-user me-2"></i>Nom complet
              </label>
              <input
                type="text"
                class="form-control"
                id="nom"
                formControlName="nom"
                placeholder="Votre nom complet"
                [class.is-invalid]="registerForm.get('nom')?.invalid && registerForm.get('nom')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('nom') }}
              </div>
            </div>

            <!-- Email -->
            <div class="mb-3">
              <label for="email" class="form-label">
                <i class="fas fa-envelope me-2"></i>Email
              </label>
              <input
                type="email"
                class="form-control"
                id="email"
                formControlName="email"
                placeholder="<EMAIL>"
                [class.is-invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('email') }}
              </div>
            </div>

            <!-- Mot de passe -->
            <div class="mb-3">
              <label for="password" class="form-label">
                <i class="fas fa-lock me-2"></i>Mot de passe
              </label>
              <input
                type="password"
                class="form-control"
                id="password"
                formControlName="password"
                placeholder="Minimum 6 caractères"
                [class.is-invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('password') }}
              </div>
            </div>

            <!-- Confirmation mot de passe -->
            <div class="mb-4">
              <label for="confirmPassword" class="form-label">
                <i class="fas fa-lock me-2"></i>Confirmer le mot de passe
              </label>
              <input
                type="password"
                class="form-control"
                id="confirmPassword"
                formControlName="confirmPassword"
                placeholder="Répétez votre mot de passe"
                [class.is-invalid]="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('confirmPassword') }}
              </div>
            </div>

            <!-- Bouton d'inscription -->
            <button
              type="submit"
              class="btn btn-primary w-100 py-2 mb-3"
              [disabled]="isLoading"
            >
              <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i *ngIf="!isLoading" class="fas fa-user-plus me-2"></i>
              {{ isLoading ? 'Inscription...' : 'Créer mon compte' }}
            </button>
          </form>

          <!-- Lien vers connexion -->
          <div class="text-center">
            <p class="mb-0">
              Déjà un compte ?
              <a routerLink="/auth/login" class="text-primary text-decoration-none fw-bold">
                Se connecter
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
