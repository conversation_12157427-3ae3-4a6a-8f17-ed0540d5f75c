<div class="container" style="margin-top: 80px; position: relative;">

  <div class="mb-4 d-flex justify-content-end">
    <button class="btn btn-success" (click)="ouvrirFormulairePourAjout()">
      <i class="fas fa-plus"></i> Ajouter un poisson
    </button>
  </div>

  <div *ngIf="poissons.length === 0">
    <p>Aucun poisson trouvé.</p>
  </div>

  <div class="row">
    <div class="col-12" *ngFor="let poisson of poissons" style="margin-bottom: 30px;">
      <div class="card position-relative" style="border-radius: 10px; box-shadow: 0px 4px 10px rgba(0,0,0,0.1); cursor: pointer;"
           (click)="ouvrirCarteDetail(poisson)">
        <div class="row no-gutters">
          <div class="col-md-4">
            <img [src]="'http://localhost:8081/backend/' + poisson.imageUrl" alt="image du poisson" style="max-width: 100%; max-height: 200px; border-radius: 10px 0 0 10px;" />
          </div>
          <div class="col-md-5 d-flex flex-column justify-content-center">
            <div class="card-body">
              <h4>{{ poisson.nom }}</h4>
              <p>Poids : {{ poisson.poids }} kg</p>
              <span class="badge bg-light text-primary">
                <i class="fas fa-fish me-1"></i>{{ poisson.espece }}
              </span>
            </div>
          </div>
          <div class="col-md-3 d-flex flex-column align-items-center justify-content-center">
            <h5 class="text-primary fw-bold">{{ poisson.prix }} DT</h5>

            <button class="btn btn-outline-primary btn-sm mt-2" (click)="ouvrirFormulaire(poisson); $event.stopPropagation()">
              <i class="fas fa-edit"></i> Modifier
            </button>

            <button class="btn btn-outline-danger btn-sm mt-2" (click)="supprimerPoisson(poisson.id); $event.stopPropagation()">
              <i class="fas fa-trash-alt"></i> Supprimer
            </button>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="modeAjout || poissonSelectionneForm" class="modal-overlay" (click)="fermerFormulaire()">
    <div class="modal-form" (click)="$event.stopPropagation()">
      <h5>{{ modeAjout ? 'Ajouter un nouveau poisson' : 'Modifier le poisson' }}</h5>

      <form [formGroup]="formPoisson" (ngSubmit)="modeAjout ? ajouterPoisson() : enregistrerModification()">

        <div class="mb-2">
          <label>Nom</label>
          <input class="form-control" formControlName="nom" />
        </div>
        <div class="mb-2">
          <label>Poids (kg)</label>
          <input type="number" class="form-control" formControlName="poids" />
        </div>
        <div class="mb-2">
          <label>Espèce</label>
          <input class="form-control" formControlName="espece" />
        </div>
        <div class="mb-2">
          <label>Prix (DT)</label>
          <input type="number" class="form-control" formControlName="prix" />
        </div>
        <div class="mb-2">
          <label>Image (png/jpg)</label>
          <input type="file" accept="image/*" (change)="onFileSelected($event)" />
        </div>
        <div class="mb-2" *ngIf="imagePreview">
          <label>Aperçu image sélectionnée :</label>
          <img
            [src]="imagePreview"
            alt="Aperçu"
            style="max-width: 100%; max-height: 200px; border-radius: 5px;"
          />
        </div>
        <div class="d-flex justify-content-end mt-3">
          <button type="button" class="btn btn-secondary me-2" (click)="fermerFormulaire()">Annuler</button>
          <button type="submit" class="btn btn-primary" [disabled]="formPoisson.invalid">
            {{ modeAjout ? 'Ajouter' : 'Enregistrer' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <div *ngIf="poissonSelectionneDetail" class="modal-overlay" (click)="fermerCarteDetail()">
    <div class="modal-form" (click)="$event.stopPropagation()" style="position: relative;">

      <div style="position: relative; display: inline-block;">
        <button
          class="btn-close"
          (click)="fermerCarteDetail()"
          title="Fermer"
          style="
            position: absolute;
            top: 5px;
            right: 5px;
            background: white;
            border-radius: 50%;
            border: none;
            font-size: 1.2rem;
            width: 30px;
            height: 30px;
            cursor: pointer;
            z-index: 10;
          ">
          &times;
        </button>

        <img
          [src]="'http://localhost:8081/backend/' + poissonSelectionneDetail.imageUrl"
          alt="Image du poisson"
          class="card-img"
          style="border-radius: 10px; margin-bottom: 15px;"
        />
      </div>

      <h4 class="card-title">{{ poissonSelectionneDetail.nom }}</h4>
      <p class="card-text"><strong>Poids :</strong> {{ poissonSelectionneDetail.poids }} kg</p>

      <span class="badge bg-light text-primary" style="font-size: 1rem;">
        <i class="fas fa-fish me-1"></i>{{ poissonSelectionneDetail.espece }}
      </span>

      <p class="card-text mt-3"><strong>Prix :</strong> {{ poissonSelectionneDetail.prix }} DT</p>
    </div>
  </div>

</div>
<div class="container" style="margin-top: 80px; position: relative;">

  <div class="mb-4 d-flex justify-content-end">
    <button class="btn btn-success" (click)="ouvrirFormulairePourAjout()">
      <i class="fas fa-plus"></i> Ajouter un poisson
    </button>
  </div>

  <div *ngIf="poissons.length === 0">
    <p>Aucun poisson trouvé.</p>
  </div>

  <div class="row">
    <div class="col-12" *ngFor="let poisson of poissons" style="margin-bottom: 30px;">
      <div class="card position-relative" style="border-radius: 10px; box-shadow: 0px 4px 10px rgba(0,0,0,0.1); cursor: pointer;"
           (click)="ouvrirCarteDetail(poisson)">
        <div class="row no-gutters">
          <div class="col-md-4">
            <img [src]="'http://localhost:8081/backend/' + poisson.imageUrl" alt="image du poisson" style="max-width: 100%; max-height: 200px; border-radius: 10px 0 0 10px;" />
          </div>
          <div class="col-md-5 d-flex flex-column justify-content-center">
            <div class="card-body">
              <h4>{{ poisson.nom }}</h4>
              <p>Poids : {{ poisson.poids }} kg</p>
              <span class="badge bg-light text-primary">
                <i class="fas fa-fish me-1"></i>{{ poisson.espece }}
              </span>
            </div>
          </div>
          <div class="col-md-3 d-flex flex-column align-items-center justify-content-center">
            <h5 class="text-primary fw-bold">{{ poisson.prix }} DT</h5>

            <button class="btn btn-outline-primary btn-sm mt-2" (click)="ouvrirFormulaire(poisson); $event.stopPropagation()">
              <i class="fas fa-edit"></i> Modifier
            </button>

            <button class="btn btn-outline-danger btn-sm mt-2" (click)="supprimerPoisson(poisson.id); $event.stopPropagation()">
              <i class="fas fa-trash-alt"></i> Supprimer
            </button>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="modeAjout || poissonSelectionneForm" class="modal-overlay" (click)="fermerFormulaire()">
    <div class="modal-form" (click)="$event.stopPropagation()">
      <h5>{{ modeAjout ? 'Ajouter un nouveau poisson' : 'Modifier le poisson' }}</h5>

      <form [formGroup]="formPoisson" (ngSubmit)="modeAjout ? ajouterPoisson() : enregistrerModification()">

        <div class="mb-2">
          <label>Nom</label>
          <input class="form-control" formControlName="nom" />
        </div>
        <div class="mb-2">
          <label>Poids (kg)</label>
          <input type="number" class="form-control" formControlName="poids" />
        </div>
        <div class="mb-2">
          <label>Espèce</label>
          <input class="form-control" formControlName="espece" />
        </div>
        <div class="mb-2">
          <label>Prix (DT)</label>
          <input type="number" class="form-control" formControlName="prix" />
        </div>
        <div class="mb-2">
          <label>Image (png/jpg)</label>
          <input type="file" accept="image/*" (change)="onFileSelected($event)" />
        </div>
        <div class="mb-2" *ngIf="imagePreview">
          <label>Aperçu image sélectionnée :</label>
          <img
            [src]="imagePreview"
            alt="Aperçu"
            style="max-width: 100%; max-height: 200px; border-radius: 5px;"
          />
        </div>
        <div class="d-flex justify-content-end mt-3">
          <button type="button" class="btn btn-secondary me-2" (click)="fermerFormulaire()">Annuler</button>
          <button type="submit" class="btn btn-primary" [disabled]="formPoisson.invalid">
            {{ modeAjout ? 'Ajouter' : 'Enregistrer' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <div *ngIf="poissonSelectionneDetail" class="modal-overlay" (click)="fermerCarteDetail()">
    <div class="modal-form" (click)="$event.stopPropagation()" style="position: relative;">

      <div style="position: relative; display: inline-block;">
        <button
          class="btn-close"
          (click)="fermerCarteDetail()"
          title="Fermer"
          style="
            position: absolute;
            top: 5px;
            right: 5px;
            background: white;
            border-radius: 50%;
            border: none;
            font-size: 1.2rem;
            width: 30px;
            height: 30px;
            cursor: pointer;
            z-index: 10;
          ">
          &times;
        </button>

        <img
          [src]="'http://localhost:8081/backend/' + poissonSelectionneDetail.imageUrl"
          alt="Image du poisson"
          class="card-img"
          style="border-radius: 10px; margin-bottom: 15px;"
        />
      </div>

      <h4 class="card-title">{{ poissonSelectionneDetail.nom }}</h4>
      <p class="card-text"><strong>Poids :</strong> {{ poissonSelectionneDetail.poids }} kg</p>

      <span class="badge bg-light text-primary" style="font-size: 1rem;">
        <i class="fas fa-fish me-1"></i>{{ poissonSelectionneDetail.espece }}
      </span>

      <p class="card-text mt-3"><strong>Prix :</strong> {{ poissonSelectionneDetail.prix }} DT</p>
    </div>
  </div>

</div>
