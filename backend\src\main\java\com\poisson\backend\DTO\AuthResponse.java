package com.poisson.backend.DTO;

import com.poisson.backend.Entity.User;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuthResponse {

    private String token;
    private String type = "Bearer";
    private Long id;
    private String nom;
    private String email;
    private String role;

    public AuthResponse(String token, User user) {
        this.token = token;
        this.id = user.getId();
        this.nom = user.getNom();
        this.email = user.getEmail();
        this.role = user.getRole().name();
    }
}
