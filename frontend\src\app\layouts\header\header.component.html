<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
  <div class="container-fluid">
    <!-- Brand -->
    <a class="navbar-brand fw-bold" routerLink="/home">
      <i class="fas fa-fish me-2"></i>
      miniProjetPoisson
    </a>

    <button
      class="navbar-toggler"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#navbarSupportedContent"
      aria-controls="navbarSupportedContent"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link" routerLink="/home" routerLinkActive="active" role="link">
            <i class="fas fa-home me-1"></i>Accueil
          </a>
        </li>
        <li class="nav-item" *ngIf="isAuthenticated">
          <a class="nav-link" routerLink="/poissons" routerLinkActive="active" role="link">
            <i class="fas fa-fish me-1"></i>Poissons
          </a>
        </li>
      </ul>

      <!-- Section authentification -->
      <div class="d-flex align-items-center">
        <!-- Si utilisateur non connecté -->
        <div *ngIf="!isAuthenticated" class="d-flex gap-2">
          <a routerLink="/auth/login" class="btn btn-outline-light btn-sm">
            <i class="fas fa-sign-in-alt me-1"></i>
            Connexion
          </a>
          <a routerLink="/auth/register" class="btn btn-light btn-sm">
            <i class="fas fa-user-plus me-1"></i>
            Inscription
          </a>
        </div>

        <!-- Si utilisateur connecté -->
        <div *ngIf="isAuthenticated && currentUser" class="dropdown">
          <button
            class="btn btn-outline-light dropdown-toggle"
            type="button"
            id="userDropdown"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="fas fa-user me-1"></i>
            {{ currentUser.nom }}
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li>
              <span class="dropdown-item-text">
                <small class="text-muted">{{ currentUser.email }}</small>
              </span>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
              <button class="dropdown-item" (click)="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>
                Déconnexion
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</nav>
