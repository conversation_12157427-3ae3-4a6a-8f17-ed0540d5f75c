.hero-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="fish-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(102,126,234,0.05)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23fish-pattern)"/></svg>');
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 2rem 0;
}

.display-4 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out;
}

.lead {
  font-size: 1.25rem;
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.btn {
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
  border: 2px solid #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.features-list {
  animation: fadeInUp 1s ease-out 0.6s both;
}

.feature-item {
  font-size: 1.1rem;
  color: #495057;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
}

.feature-item:hover {
  transform: translateX(10px);
  color: #667eea;
}

.hero-image {
  position: relative;
  z-index: 2;
}

.fish-animation {
  animation: float 3s ease-in-out infinite, fadeInRight 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@media (max-width: 991.98px) {
  .hero-section {
    padding: 2rem 0;
  }

  .display-4 {
    font-size: 2.5rem;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 3rem;
  }

  .d-flex.gap-3 {
    flex-direction: column;
    align-items: center;
  }

  .btn-lg {
    width: 100%;
    max-width: 300px;
  }

  .fish-animation {
    margin-top: 2rem;
  }

  .fa-10x {
    font-size: 6rem !important;
  }
}